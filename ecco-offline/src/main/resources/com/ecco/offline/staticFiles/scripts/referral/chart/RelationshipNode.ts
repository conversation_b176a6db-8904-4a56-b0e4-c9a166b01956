
import {RelatedRelationship} from "ecco-dto";
import dynamicTree = require("../../draw/dynamic-tree");
import Node = dynamicTree.DynamicTreeNode;
import events = require("./events");
import RelationshipClickedEvent = events.RelationshipClickedEvent;

class RelationshipNode {

    private node: Node;

    constructor(private referral: RelatedRelationship, private treeControl: dynamicTree.DynamicTreeControl) {
        var title = referral &&
            (referral.clientDisplayName.replace(" ","\n") + "\n(" + (referral.relationship || "primary") + ")");
        title = title || "(no referral)";
        this.node = new Node(title);
        this.node.addClickEventHandler( (event) => { this.referralClicked();} );
    }

    private referralClicked() {
        RelationshipClickedEvent.bus.fire( new RelationshipClickedEvent(this) );
        //this.treeControl.setContextNode(this.node);
    }

    /** Add a related referral as a child of this one.
     */
    public addChild(related: RelatedRelationship) {
        var child = new RelationshipNode(related, this.treeControl);
        this.node.addChild(child.getNode());
    }

    public getNode(): Node {
        return this.node;
    }

    public getReferral(): RelatedRelationship {
        return this.referral;
    }
}
export = RelationshipNode;