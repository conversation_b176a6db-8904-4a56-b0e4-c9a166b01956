///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import types = require("./types");
import Group = types.Group;
import GroupedAnalysis = types.GroupedAnalysis;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import GroupFn = types.GroupFn;
import referralCommonAnalysis = require("./referralCommonAnalysis");
import groupByReferralStatus = referralCommonAnalysis.groupByReferralStatus;
import groupByReferredService = referralCommonAnalysis.groupByReferredService;
import groupByReferralProjectName = referralCommonAnalysis.groupByReferralProjectName;
import groupByAgeAtDateOfReferral = referralCommonAnalysis.groupByAgeAtDateOfReferral;
import groupByReferralAssignedWorker = referralCommonAnalysis.groupByReferralAssignedWorker;
import {SupportWork} from "ecco-dto";
import tableRepresentations = require("../tables/predefined-table-representations");
import smartStepCount = require("./smartStepCount");
import {AnalysisContext} from "../chart-domain";

export interface ReferralAggregateGroupWithSmartStepCounts extends Group<ReferralAggregate> {
    totalAchievedSmartSteps: number;
    totalOutstandingSmartSteps: number;
}

export function smartStepCountsByWorker(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<ReferralAggregateGroupWithSmartStepCounts> {
    return smartStepCountsReport(input, ctx, groupByReferralAssignedWorker);
}
export function smartStepCountsByAgeAtReferralDate(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<ReferralAggregateGroupWithSmartStepCounts> {
    return smartStepCountsReport(input, ctx, groupByAgeAtDateOfReferral);
}
export function smartStepCountsByProject(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<ReferralAggregateGroupWithSmartStepCounts> {
    return smartStepCountsReport(
        input,
        ctx,
        groupByReferralProjectName as GroupFn<ReferralAggregate>
    );
}
export function smartStepCountsByService(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<ReferralAggregateGroupWithSmartStepCounts> {
    return smartStepCountsReport(input, ctx, groupByReferredService as GroupFn<ReferralAggregate>);
}
export function smartStepCountsByStatus(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<ReferralAggregateGroupWithSmartStepCounts> {
    return smartStepCountsReport(input, ctx, groupByReferralStatus);
}

function notNull(entry: any) {
    return entry != null;
}

function smartStepCountsReport(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext,
    groupFn: GroupFn<ReferralAggregate>
): Sequence<ReferralAggregateGroupWithSmartStepCounts> {
    return groupFn(input, ctx).map(pair => {
        const elements: Sequence<ReferralAggregate> = pair.elements;
        const allWork: Sequence<SupportWork> = elements
            .map(input => input.supportWork)
            .filter(notNull)
            .flatten<SupportWork>();
        const allActionsWithWork = smartStepCount.smartStepCountsFromSupportWork(allWork);
        const count = new smartStepCount.SmartStepCount(allActionsWithWork);
        const countAchievedSmartSteps = count.getTotalAchievedSmartSteps();
        const countOutstandingSmartSteps = count.getTotalOutstandingSmartSteps();
        const result: ReferralAggregateGroupWithSmartStepCounts = {
            key: pair.key,
            totalAchievedSmartSteps: countAchievedSmartSteps,
            totalOutstandingSmartSteps: countOutstandingSmartSteps,
            elements: elements,
            count: countAchievedSmartSteps + countOutstandingSmartSteps
        };
        return result;
    });
}
export class ReferralGroupWithSmartStepsAnalysis extends GroupedAnalysis<
    ReferralAggregate,
    ReferralAggregateGroupWithSmartStepCounts
> {
    constructor(ctx: AnalysisContext, data: Sequence<ReferralAggregateGroupWithSmartStepCounts>) {
        super(ctx, data);
        this.recordRepresentation = {
            SmartStepCountsAnalysis: tableRepresentations.smartStepCountsAnalysisColumns
        };
        this.addOnClickAnalyser("ungroup", "WrapUnGroupReferralAggregateSequenceAnalyser");
        this.addOnClickManyAnalysis("ungroup", "ReferralAggregateAnalysis");
    }
}
export var smartStepCountsByWorkerAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithSmartStepCounts
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithSmartStepCounts> {
    return new ReferralGroupWithSmartStepsAnalysis(ctx, smartStepCountsByWorker(input, ctx));
};
export var smartStepCountsByAgeAtReferralAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithSmartStepCounts
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithSmartStepCounts> {
    return new ReferralGroupWithSmartStepsAnalysis(
        ctx,
        smartStepCountsByAgeAtReferralDate(input, ctx)
    );
};
export var smartStepCountsByProjectAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithSmartStepCounts
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithSmartStepCounts> {
    return new ReferralGroupWithSmartStepsAnalysis(ctx, smartStepCountsByProject(input, ctx));
};
export var smartStepCountsByServiceAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithSmartStepCounts
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithSmartStepCounts> {
    return new ReferralGroupWithSmartStepsAnalysis(ctx, smartStepCountsByService(input, ctx));
};
export var smartStepCountsByStatusAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithSmartStepCounts
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithSmartStepCounts> {
    return new ReferralGroupWithSmartStepsAnalysis(ctx, smartStepCountsByStatus(input, ctx));
};
