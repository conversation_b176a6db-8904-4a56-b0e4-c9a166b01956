import * as evidenceDto from "ecco-dto/evidence-dto";
import {
    columnMap,
    ColumnRepresentation,
    dateTimeColumn,
    dateTimeColumnFromIsoUtc,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";

import {EccoDateTime, SparseArray, StringUtils} from "@eccosolutions/ecco-common";
import {ReferralSummaryDto} from "ecco-dto/referral-dto";
import {
    BaseWork,
    Client,
    ConfigResolverDefault,
    QuestionAnswerSnapshotDto,
    QuestionnaireAnswersSnapshotDto,
    QuestionnaireWorkDto,
    ReferralDto,
    ServiceRecipient,
    SessionData
} from "ecco-dto";
import {AnalysisContext} from "../chart-domain";
import {
    clientOnlyColumns,
    countsBy,
    ensureEntityReferencesParent,
    evidenceTaskNameLookup,
    referralAggregateReportItemColumns,
    referralReportItemColumns,
    referralSummaryColumns,
    serviceRecipientColumns
} from "../tables/predefined-table-representations";
import {getServiceAllocation} from "./referralCommonAnalysis";
import {
    CountsByMonthDto,
    EntityWithParent,
    MatrixAnalysis,
    MatrixRow,
    NumberAnalysis,
    QnAnswerWork,
    QnAnswerWorkParent,
    QnAnswerWorkWithRefToReferralAggregate
} from "./types";
import {ByMonthAnalysis} from "./groupedSummaryCommonAnalysis";
import {ReferralSummaryAnalysis} from "./referralAnalysis";
import {baseWorkOnlyColumns} from "./workCommonAnalysis";
import Lazy = require("lazy");
import tableRepresentations = require("../tables/predefined-table-representations");
import types = require("./types");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Accumulator = types.Accumulator;
import Analyser = types.Analyser;
import extractPair = types.extractPair;
import Group = types.Group;
import GroupFn = types.GroupFn;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import {ColumnRepresentationsMap} from "../ReportTable";


//*********************************
// Analysis: QnAnswerAnalysis
// deals with questionnaire work

const qnAnswerWorkOnlyColumns = columnMap(
    numberColumn<QnAnswerWork>("sr-id", row => row.work?.serviceRecipientId),
    textColumn<QnAnswerWork>("w-id", row => row.work?.id),
    numberColumn<QnAnswerWork>("q-id", row => row.qnAnswer?.questionId),
    textColumn<QnAnswerWork>("author", row => row.work?.authorDisplayName),
    textColumn<QnAnswerWork>("worker", row => row.work?.authorDisplayName),
    dateTimeColumnFromIsoUtc<QnAnswerWork>("created", row => row.work?.createdDate || null),
    dateTimeColumn<QnAnswerWork>("work date", row =>
        EccoDateTime.parseIso8601(row.work?.workDate || null)
    ),
    textColumn<QnAnswerWork>("task", row =>
        evidenceTaskNameLookup(row.work?.taskName || null, row.work?.serviceAllocationId || null)
    ),
    textColumn<QnAnswerWork>("questionnaire", row =>
        tableRepresentations.evidenceTaskNameLookup(
            row.work?.taskName || null,
            row.work?.serviceAllocationId || null
        )
    ),
    textColumn<QnAnswerWork>("questiongroup", (row, ctx) =>
        tableRepresentations.questionGroupDefByQuestionIdLookup(
            row.qnAnswer?.questionId!,
            ctx.getSessionData()
        )
    ),
    textColumn<QnAnswerWork>("question", (row, ctx) =>
        tableRepresentations.questionDefIdLookup(row.qnAnswer?.questionId!, ctx.getSessionData())
    ),
    textColumn<QnAnswerWork>("answer", (row, ctx) =>
        tableRepresentations.questionAnswerDisplayValueLookup(
            row.qnAnswer?.questionId!,
            row.qnAnswer?.answer,
            ctx.getSessionData()
        )
    ),
    textColumn<QnAnswerWork>("comment", (row, ctx) => row.work?.comment || null)
);

export function flattenToAnswers(input: Sequence<QuestionnaireWorkDto>): Sequence<QnAnswerWork> {
    return input
        .filter(q => q != null)
        .map(q =>
            q.answers.map(ans => {
                const result: QnAnswerWork = {
                    work: q,
                    qnAnswer: ans
                };
                return result;
            })
        )
        .flatten<QnAnswerWork>();
}

export var flattenToAnswersAnalyser: Transformer<QuestionnaireWorkDto, QnAnswerWork> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionnaireWorkDto>
): SequenceAnalysis<QnAnswerWork> {
    return new QnAnswerOnlyAnalysis(ctx, flattenToAnswers(input));
};

export class QnAnswerOnlyAnalysis extends SequenceAnalysis<QnAnswerWork> {
    constructor(ctx: AnalysisContext, data: Sequence<QnAnswerWork>) {
        super(ctx, data, (item: QnAnswerWork) => item.work!.id);
        this.recordRepresentation = {
            // NB instead of adding QnAnswerWorkWithStaff to get the staff,
            // we can simply use qnAnswerWorkOnlyColumns and use the a field object definition
            // since the work item includes a reference to staff?: StaffDto
            // {
            //     "title": "staff",
            //         "representation": "_text",
            //         "path": [
            //              "staff",
            //              "displayName"
            //          ]
            // },
            QnAnswerWork: qnAnswerWorkOnlyColumns
        };
        this.derivativeAnalysers = {
            questionAnswersTransposeByWork: (ctx, input) => {
                const fillBlankData = input.map(d => ensureEntityReferencesParent(d, null));
                return questionAnswersTransposeByWorkAnalyser(ctx, fillBlankData);
            }
        };
    }
}

export class QnAnswerAnalysis extends SequenceAnalysis<QuestionnaireWorkDto> {
    constructor(ctx: AnalysisContext, data: Sequence<QuestionnaireWorkDto>) {
        super(ctx, data, (item: QuestionnaireWorkDto) => item.id);
        this.recordRepresentation = {};
        this.derivativeAnalysers = {
            flattenToAnswers: flattenToAnswersAnalyser,
            questionWorkToSrIds: questionWorkToSrIdAnalyser
        };
    }
}
const questionWorkToSrIdAnalyser: Analyser<
    Sequence<QuestionnaireWorkDto>,
    Sequence<number>
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionnaireWorkDto>
): SequenceAnalysis<number> {
    return new NumberAnalysis(ctx, uniqueSrId(input));
};
export function uniqueSrId(input: Sequence<QuestionnaireWorkDto>): Sequence<number> {
    let uniqSrId: Sequence<number> = input.map(work => work.serviceRecipientId).uniq();
    return uniqSrId;
}

const qnAnswerWorkColumns = columnMap(
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "rid",
        row => row.parent.referral.referralCode || row.parent.referral.referralId.toString()
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "cid",
        row => row.parent.referral.clientCode || row.parent.referral.clientId.toString()
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "service",
        (row, ctx) =>
            ctx.getSessionData().getServiceCategorisation(row.parent.referral.serviceAllocationId)
                .serviceName
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("company", row => {
        const allocation = getServiceAllocation(row.parent);
        return allocation == null ? "not allocated" : allocation.companyName;
    }),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("service group", row => {
        const allocation = getServiceAllocation(row.parent);
        return allocation == null ? "not allocated" : allocation.serviceGroupName;
    }),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("client group", row => {
        const allocation = getServiceAllocation(row.parent);
        return allocation == null ? "not allocated" : allocation.clientGroupName;
    }),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "project",
        (row, ctx) =>
            ctx.getSessionData().getServiceCategorisation(row.parent.referral.serviceAllocationId)
                .projectName
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "location",
        (row, ctx) =>
            ctx.getSessionData().getServiceCategorisation(row.parent.referral.serviceAllocationId)
                .projectName
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("wid", row => row.work?.id),
    numberColumn<QnAnswerWorkWithRefToReferralAggregate>("qid", row => row.qnAnswer?.questionId),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "author",
        row => row.work?.authorDisplayName
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "worker",
        row => row.parent.referral.supportWorkerDisplayName
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>(
        "client",
        row => row.parent.referral.clientDisplayName
    ),
    dateTimeColumnFromIsoUtc<QnAnswerWorkWithRefToReferralAggregate>(
        "created",
        row => row.work?.createdDate || null
    ),
    dateTimeColumn<QnAnswerWorkWithRefToReferralAggregate>("work date", row =>
        EccoDateTime.parseIso8601(row.work?.workDate || null)
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("questionnaire", row =>
        tableRepresentations.evidenceTaskNameLookup(
            row.work?.taskName || null,
            row.work?.serviceAllocationId || null
        )
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("task", row =>
        tableRepresentations.evidenceTaskNameLookup(
            row.work?.taskName || null,
            row.work?.serviceAllocationId || null
        )
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("questiongroup", row =>
        tableRepresentations.questionGroupDefByQuestionIdLookup(
            row.qnAnswer?.questionId!,
            row.parent.sessionData!
        )
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("question", row =>
        tableRepresentations.questionDefIdLookup(row.qnAnswer?.questionId!, row.parent.sessionData!)
    ),
    textColumn<QnAnswerWorkWithRefToReferralAggregate>("answer", row =>
        tableRepresentations.questionAnswerDisplayValueLookup(
            row.qnAnswer?.questionId!,
            row.qnAnswer?.answer,
            row.parent.sessionData!
        )
    )
);
const questionAnswerWorkToRefAggregateColumns = joinNestedPathColumnMaps<
    QnAnswerWorkWithRefToReferralAggregate,
    ReferralAggregate
>("r", row => row.parent, referralAggregateReportItemColumns);
export const qnAnswerWorkWithRefAggregateColumns = joinColumnMaps(
    qnAnswerWorkColumns,
    questionAnswerWorkToRefAggregateColumns
);

// ****************************
// TRANSPOSED QUESTIONNAIRE

// also see https://github.com/softwareventures/table/blob/main/index.ts
interface TransposedQnAnswerWorkWithRefToReferralAggregate
    extends TransposedQnAnswerWorkParent<ReferralAggregate> {}
interface TransposedQnAnswerWorkParent<P> extends EntityWithParent<P, TransposedQnAnswerWork> {}
interface TransposedQnAnswerWork extends QnAnswerWork {
    unique: string; // group key
    questionAnswers: SparseArray<string>; // [question id] = answer
}
// for questionAnswers
function transposedQnAnswerWorkQnNameColumns<T>(
    qnIds: number[],
    sessionData: SessionData
): ColumnRepresentation<TransposedQnAnswerWorkParent<T>>[] {
    //textColumn<TransposedQnAnswerWorkWithRefToReferralAggregate>("rid", (row) => row.reportItem.referral.referralCode || row.reportItem.referral.referralId.toString())
    return qnIds.map(qnId =>
        textColumn<TransposedQnAnswerWorkParent<T>>(
            sessionData.getQuestionById(qnId).name,
            (row, ctx) =>
                tableRepresentations.questionAnswerDisplayValueLookup(
                    qnId,
                    row.questionAnswers[qnId],
                    ctx.getSessionData()
                )
        )
    );
};
// for unique
const transposedQnAnswerWorkColumns = columnMap(
    textColumn<TransposedQnAnswerWorkWithRefToReferralAggregate>("groupBy", row => row.unique)
);
// for reportItem
const transposedQuestionAnswerWorkToRefAggregateColumns = joinNestedPathColumnMaps<
    TransposedQnAnswerWorkParent<any>, // 'any' because we're forcing to ReferralAggregate here from T (see the commit showing below can be typed to ReferralAggregate)
    ReferralAggregate
>("r", row => row.parent, referralReportItemColumns);

// transposedQnAnswerWork is itself a QnAnswerWork item to allow us to access the work when we are grouped by work
const transposedQnAnswerWorkWhenGroupByWorkColumns = joinNestedPathColumnMaps<
    TransposedQnAnswerWorkParent<ReferralAggregate>,
    BaseWork
>("w", row => row.work, baseWorkOnlyColumns);

const transposedQuestionAnswerWorkToSRColumns = joinNestedPathColumnMaps<
    TransposedQnAnswerWorkParent<any>, // 'any' because we're forcing to ReferralAggregate here from T (see the commit showing below can be typed to ReferralAggregate)
    ServiceRecipient
>("sr", row => row.parent, serviceRecipientColumns);

// join together, where transposedQnAnswerWorkQnNameColumns is added to in TransposedTransposedQnAnswerWorkAnalysis
let transposedQuestionAnswerColumns = joinColumnMaps(
    transposedQuestionAnswerWorkToSRColumns,
    transposedQuestionAnswerWorkToRefAggregateColumns,
    transposedQnAnswerWorkColumns,
    transposedQnAnswerWorkWhenGroupByWorkColumns
);

function transposedQuestionnaireColumnsData<T>(
    data: Sequence<TransposedQnAnswerWorkParent<T>>,
    ctx: AnalysisContext
): ColumnRepresentationsMap<TransposedQnAnswerWorkParent<T>> {
    const qnIds: number[] = [];
    // toArray forces early execution of the lazy chaining, so we don't create a closure
    data.toArray()
        .filter(t => !!t)
        .map(t => {
            for (let qnId in t.questionAnswers) {
                if (qnId && t.questionAnswers.hasOwnProperty(qnId)) {
                    qnIds.push(parseInt(qnId));
                }
            }
        });
    //const columns = qnIds.map(qnId => ctx.getSessionData().getQuestionById(qnId).name)
    const columns = transposedQnAnswerWorkQnNameColumns(qnIds, ctx.getSessionData());
    return columnMap(...columns);
}

function transposedQuestionnaireColumnsDef(ctx: AnalysisContext) {
    // create the question columns dynamically
    // we can't easily get the questions configured from the report
    // so we enforce the serviceId - because we can get it from there
    // if we don't do this, we end up with the report defn with question names that don't exist in the table
    if (!ctx.getServiceId()) {
        throw new Error(
            "we need a 'serviceId' on this report, to work out the question columns allowed"
        );
    }
    const questionGroupNames = ctx
        .getQuestionnaireEvidenceGroups()
        .map(qg => {
            // config for any service is good
            const svcConfig = ctx
                .getSessionData()
                .getServiceCategorisationByIds(ctx.getServiceId()!, null)!.id;
            const resolver = ConfigResolverDefault.fromLegacyServiceType(
                ctx.getSessionData().getServiceTypeByServiceCategorisationId(svcConfig),
                ctx.getSessionData()
            );
            return resolver.getQuestionGroupsFilteredForTask(qg);
        })
        .reduce((r, x) => r.concat(x), []); // flatMap
    const questionIds = questionGroupNames
        .map(qg => qg.questions.map(q => q.id))
        .reduce((r, x) => r.concat(x), []); // flatMap
    const columns = transposedQnAnswerWorkQnNameColumns(questionIds, ctx.getSessionData());
    return columnMap(...columns);
}

/** Provides dynamic column generation */
class TransposedTransposedQnAnswerWorkAnalysis<T> extends SequenceAnalysis<
    TransposedQnAnswerWorkParent<T>
> {
    constructor(
        ctx: AnalysisContext,
        data: Sequence<TransposedQnAnswerWorkParent<T>>,
        keyFn: (item: TransposedQnAnswerWorkParent<T>) => string
    ) {
        super(ctx, data, keyFn);

        // DEPRECATED approaches to dynamic columns below are based on serviceId or data, both aren't ideal
        // So, these approaches are deprecated, see columnSourceDefIds in reports-definitions for the new approach.
        // NB getRecordRepresentationFromSources was created solely to allow the stage context to determine the questionGroupId
        // we could now cast the AnalysisContext to a AnalysisStageContext to getCurrentStage criteria

        // DEPRECATED from def
        // get the columns from the service defn - but if we don't have a service, then we don't know what to do
        // because although we have the questionnaireEvidenceGroup, this is only the taskName and not the ticked questions
        let transposedQnAnswerColumnsSvc = {};
        if (ctx.getServiceId()) {
            const columnMap = transposedQuestionnaireColumnsDef(ctx);
            transposedQnAnswerColumnsSvc = joinColumnMaps(
                columnMap,
                transposedQuestionAnswerColumns
            );
        }

        // DEPRECATED from data
        // get the columns from the data - but if the definition has a column that isn't here then it will error
        // also if there is 0 results, there can be no definitions, and a render of an empty table errors.
        const columnMapData = transposedQuestionnaireColumnsData(data, ctx);
        // referral aggregate
        const transposedQnAnswerColumnsData = joinColumnMaps(
            columnMapData,
            transposedQuestionAnswerColumns
        );

        // get the columns from a questionGroupId - but this is an issue if there is 0 results, but we still want to show a table
        // this also assumes that the first question belongs to one group for the whole data - which
        // is a safe assumption in the code but not the data model - see getQuestionGroupByQuestionId
        //const columnMapDataDef = transposedQuestionnaireColumnsDataDef(data, ctx);
        //const transposedQnAnswerWorkWithRefAggregateColumnsDataDef = joinColumnMaps(columnMapDataDef, transposedQuestionAnswerWorkToRefAggregateColumns);

        // Analysis columns are set on constructor

        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            TransposedQnAnswerWork: transposedQnAnswerColumnsSvc, // legacy
            TransposedQnAnswerWorkFromDef: transposedQuestionAnswerColumns, // dynamic cols included due to getRecordRepresentationFromSources
            TransposedQnAnswerWorkData: transposedQnAnswerColumnsData // legacy
        };
    }

    // questionIds columns are gathered here triggered by the definition of "columnSourceDefIds": ["questionGroupId"]
    // NB getRecordRepresentationFromSources was created solely to allow the stage context to determine the questionGroupId
    // we could now cast the AnalysisContext to a AnalysisStageContext to getCurrentStage criteria
    override getRecordRepresentationFromSources(
        name: string,
        sources: string[]
    ): ColumnRepresentationsMap<TransposedQnAnswerWorkParent<T>> {
        // dynamic columns are triggered using this representation name
        if ("TransposedQnAnswerWorkFromDef" != name) {
            return {};
        }
        const questionIds = sources
            .map(qgIdStr =>
                this.ctx
                    .getSessionData()
                    .getQuestionGroupById(parseInt(qgIdStr))
                    .questions.map(q => q.id)
            )
            .reduce((r, x) => r.concat(x), []); // flatMap;

        const columns = transposedQnAnswerWorkQnNameColumns(questionIds, this.ctx.getSessionData());
        return columnMap(...columns);
    }
}

function questionAnswersPerGroup<T>(
    input: Sequence<QnAnswerWorkParent<T>>,
    groupFn: GroupFn<QnAnswerWorkParent<T>>,
    groupedOnWork = false
): Sequence<TransposedQnAnswerWorkParent<T>> {
    return groupFn(input)
        .map((group: Group<QnAnswerWorkParent<T>>) => {
            const allDataForGroup: Sequence<QnAnswerWorkParent<T>> = group.elements;
            // order by work date ascending (natural order), so that later answers are chosen (NB grouped by work isn't affected)
            const allDataForGroupOrdered = allDataForGroup.sort((a, b) => {
                // we now have a null value for those referrals without any data (as opposed to filtering them out)
                // so ensure we're checking nulls
                const aDte = a.work?.workDate && EccoDateTime.parseIso8601(a.work.workDate);
                const bDte = b.work?.workDate && EccoDateTime.parseIso8601(b.work.workDate);
                // return the ascending order or nulls first (as later answers are chosen)
                return aDte && bDte ? aDte.compare(bDte) : aDte ? 1 : -1;
            });
            // order by work date ascending (natural order), so that later answers are chosen (NB grouped by work isn't affected)
            const r = allDataForGroupOrdered.reduce((memo, value) => {
                // we now have a null value for those referrals without any data (as opposed to filtering them out)
                // getAnswerDisplayValue is able to show non-answered questions, so we only want to deal with those with values
                if (value.qnAnswer != null) {
                    memo[value.qnAnswer.questionId] = value.qnAnswer.answer;
                }
                return memo;
            }, {} as SparseArray<string>);
            const obj: Partial<TransposedQnAnswerWorkParent<T>> = {
                unique: group.key, // the item we are grouped on
                parent: allDataForGroupOrdered.first()?.parent,
                questionAnswers: r
            };
            // if not grouped by work, we just take the last, which is the latest
            if (!groupedOnWork) {
                obj.work = allDataForGroupOrdered.last()?.work;
            }
            if (groupedOnWork) {
                // in case we are grouped by work, then we can be sure the 'allDataForGroup' is just one row - so include it
                obj.work = allDataForGroupOrdered.first()?.work;
                obj.qnAnswer = allDataForGroupOrdered.first()?.qnAnswer;
            }
            return obj;
        })
        .flatten<TransposedQnAnswerWorkParent<T>>();
}
function groupBySrId<T extends {serviceRecipientId: number}>(
    input: Sequence<QnAnswerWorkParent<T>>
): Sequence<Group<QnAnswerWorkParent<T>>> {
    return input
        .groupBy(inputElement => inputElement.parent.serviceRecipientId.toString())
        .pairs()
        .map(extractPair);
}
function groupByCid(
    input: Sequence<QnAnswerWorkWithRefToReferralAggregate>
): Sequence<Group<QnAnswerWorkWithRefToReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.parent.referral.clientId.toString())
        .pairs()
        .map(extractPair);
}
function groupBySrid(
    input: Sequence<QnAnswerWorkWithRefToReferralAggregate>
): Sequence<Group<QnAnswerWorkWithRefToReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.parent.referral.serviceRecipientId.toString())
        .pairs()
        .map(extractPair);
}
// group by workDate (not uuid, because we only can see the 'key' currently in the fields)
function groupByWorkDate<T>(
    input: Sequence<QnAnswerWorkParent<T>>
): Sequence<Group<QnAnswerWorkParent<T>>> {
    return input
        .groupBy(inputElement => inputElement.work?.workDate.toString() || "no-date")
        .pairs()
        .map(extractPair);
}
function groupByWorkUuid<T>(
    input: Sequence<QnAnswerWorkParent<T>>
): Sequence<Group<QnAnswerWorkParent<T>>> {
    return input
        .groupBy(inputElement => inputElement.work?.id?.toString() || "no-id")
        .pairs()
        .map(extractPair);
}
function convertToQAReferralAggregate(
    data: Sequence<QuestionAnswerSnapshotDtoWithParent>
): Sequence<QnAnswerWorkWithRefToReferralAggregate> {
    return data.map(qas => {
        const result: QnAnswerWorkWithRefToReferralAggregate = {
            work: null,
            parent: {
                referral: qas.parent.referralSummary as ReferralDto,
                client: qas.parent.client
            },
            qnAnswer: {...qas}
        };
        return result;
    });
}
function cleanQAWithParent(
    data: Sequence<QuestionAnswerSnapshotDtoWithParent>
): Sequence<QnAnswerWorkParent<any>> {
    return data.map(qas => {
        const result: QnAnswerWorkParent<any> = {
            work: null,
            parent: qas.parent,
            qnAnswer: {...qas}
        };
        return result;
    });
}

const questionAnswerSnapshotTransposeBySrIdAnalyser: Transformer<
    QuestionAnswerSnapshotDtoWithParent,
    TransposedQnAnswerWorkParent<any>
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): SequenceAnalysis<TransposedQnAnswerWorkParent<ServiceRecipient>> {
    return new TransposedTransposedQnAnswerWorkAnalysis(
        ctx,
        questionAnswersPerGroup(cleanQAWithParent(input), groupBySrId),
        (item: TransposedQnAnswerWorkParent<ServiceRecipient>) => item.unique
    );
};

const questionAnswerSnapshotTransposeByCidAnalyser: Transformer<
    QuestionAnswerSnapshotDtoWithParent,
    TransposedQnAnswerWorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): SequenceAnalysis<TransposedQnAnswerWorkWithRefToReferralAggregate> {
    return new TransposedTransposedQnAnswerWorkAnalysis(
        ctx,
        questionAnswersPerGroup(convertToQAReferralAggregate(input), groupByCid),
        (item: TransposedQnAnswerWorkWithRefToReferralAggregate) => item.unique
    );
};

const questionAnswersTransposeByCidAnalyser: Transformer<
    QnAnswerWorkWithRefToReferralAggregate,
    TransposedQnAnswerWorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<QnAnswerWorkWithRefToReferralAggregate>
): SequenceAnalysis<TransposedQnAnswerWorkWithRefToReferralAggregate> {
    return new TransposedTransposedQnAnswerWorkAnalysis(
        ctx,
        questionAnswersPerGroup(input, groupByCid),
        (item: TransposedQnAnswerWorkWithRefToReferralAggregate) => item.unique
    );
};

const questionAnswersTransposeBySridAnalyser: Transformer<
    QnAnswerWorkWithRefToReferralAggregate,
    TransposedQnAnswerWorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<QnAnswerWorkWithRefToReferralAggregate>
): SequenceAnalysis<TransposedQnAnswerWorkWithRefToReferralAggregate> {
    return new TransposedTransposedQnAnswerWorkAnalysis(
        ctx,
        questionAnswersPerGroup(input, groupBySrid),
        (item: TransposedQnAnswerWorkWithRefToReferralAggregate) => item.unique
    );
};

// function as a whole is of the type: Transformer<QnAnswerWork, TransposedQnAnswerWork>
function questionAnswersTransposeByWorkAnalyser<T>(
    ctx: AnalysisContext,
    input: Sequence<QnAnswerWorkParent<T>>
): SequenceAnalysis<TransposedQnAnswerWorkParent<T>> {
    return new TransposedTransposedQnAnswerWorkAnalysis<T>(
        ctx,
        questionAnswersPerGroup(input, groupByWorkDate, true),
        (item: TransposedQnAnswerWorkParent<T>) => item.unique
    );
};

const questionAnswersWithRATransposeByWorkAnalyser: Transformer<
    QnAnswerWorkWithRefToReferralAggregate,
    TransposedQnAnswerWorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<QnAnswerWorkWithRefToReferralAggregate>
): SequenceAnalysis<TransposedQnAnswerWorkWithRefToReferralAggregate> {
    return new TransposedTransposedQnAnswerWorkAnalysis(
        ctx,
        questionAnswersPerGroup(input, groupByWorkDate, true),
        (item: TransposedQnAnswerWorkWithRefToReferralAggregate) => item.unique
    );
};
// ****************************

export class QnAnswerWithRefAnalysis extends SequenceAnalysis<QnAnswerWorkWithRefToReferralAggregate> {
    constructor(ctx: AnalysisContext, data: Sequence<QnAnswerWorkWithRefToReferralAggregate>) {
        super(ctx, data, (item: QnAnswerWorkWithRefToReferralAggregate) => item.work!.id);
        this.derivativeAnalysers = {
            questionAnswersTranspose: questionAnswersTransposeByCidAnalyser, // legacy
            questionAnswersTransposeByCid: questionAnswersTransposeByCidAnalyser,
            questionAnswersTransposeBySrid: questionAnswersTransposeBySridAnalyser,
            questionAnswersTransposeByWork: questionAnswersWithRATransposeByWorkAnalyser,
            questionAnswersCountByQuestion: questionAnswersCountByQuestionAnalyser,
            questionAnswersCountByQuestionAnswerTotals:
                questionAnswersCountByQuestionAnswerTotalsAnalyser
        };
        this.recordRepresentation = {
            QnAnswerWork: qnAnswerWorkWithRefAggregateColumns
        };
    }
}

interface GroupQnAnswerTotals extends Group<QnAnswerWorkWithRefToReferralAggregate> {
    numberTotal: number;
}
// count of the number of answer totals per question
export var questionAnswersCountByQuestionAnswerTotalsAnalyser: Transformer<
    QnAnswerWorkWithRefToReferralAggregate,
    GroupQnAnswerTotals
> = function (
    ctx: AnalysisContext,
    input: Sequence<QnAnswerWorkWithRefToReferralAggregate>
): SequenceAnalysis<GroupQnAnswerTotals> {
    return new GroupQnAnswersTotalsAnalysis(
        ctx,
        totalsByQn(ctx, input, a => a.qnAnswer?.questionId.toString() || "no answer")
    );
};
function totalsByQn<T extends QnAnswerWorkWithRefToReferralAggregate>(
    ctx: AnalysisContext,
    input: Sequence<T>,
    extractKey: (input: T) => string
): Sequence<GroupQnAnswerTotals> {
    return input
        .groupBy(inputElement => extractKey(inputElement))
        .pairs()
        .map(extractPair)
        .map(pair => {
            /*const numberTotal = pair.elements
                        .filter(e => e.qnAnswer != null)
                        .filter(e => {
                            const qn = ctx.getSessionData().getQuestionById(e.qnAnswer.questionId);
                            const qnType = SessionData.questionType(qn, false); // choices / list / a freeType
                            return qnType == "number";
                        })
                        .map(e => e.qnAnswer.answer ? parseInt(e.qnAnswer.answer) : 0)
                        .reduce((a,b) => {
                            return a + b;
                        }, 0);

                const checkboxTotal = pair.elements
                        .filter(e => e.qnAnswer != null)
                        .filter(e => {
                            const qn = ctx.getSessionData().getQuestionById(e.qnAnswer.questionId);
                            const qnType = SessionData.questionType(qn, false); // choices / list / a freeType
                            return qnType == "checkbox";
                        })
                        .filter(e => e.qnAnswer.answer !== undefined && e.qnAnswer.answer.toLowerCase() == "true")
                        .reduce((a,b) => {
                            return a + 1;
                        }, 0);*/

            const numTot = pair.elements
                .filter(e => e.qnAnswer != null)
                // NB the grouping is by question, but might not be - so just check
                .filter(e => e.qnAnswer!.questionId.toString() == pair.key)
                .reduce((curr, next) => {
                    const qn = ctx.getSessionData().getQuestionById(next.qnAnswer!.questionId);
                    const qnType = SessionData.questionType(qn, false); // choices / list / a freeType
                    // a freeType can be "textarea" / "text" / "date" / "number" / "checkbox" / "money" / "integer" / "list" / "markdown"
                    switch (qnType) {
                        case "number":
                            return curr + parseInt(next.qnAnswer!.answer);
                        case "checkbox":
                            const ans = ctx
                                .getSessionData()
                                .getAnswerDisplayValue(qn.id, next.qnAnswer!.answer);
                            return curr + (ans != "-" ? 1 : 0);
                        default:
                            // see SessionData.getAnswerDisplayValue for choices / list / checkbox
                            return 0;
                    }
                }, 0);

            const ret: GroupQnAnswerTotals = {
                key:
                    StringUtils.trimText(
                        ctx.getSessionData().getQuestionById(parseInt(pair.key)).name,
                        10
                    ) || "unknown",
                count: pair.elements.size(),
                elements: pair.elements,
                numberTotal: numTot
                // numberTotal || checkboxTotal,
            };
            return ret;
        });
}
/*function totalsByQn<T extends QnAnswerWorkWithRefToReferralAggregate>(ctx: AnalysisContext, input: Sequence<T>, extractKey: (input: T) => string): Sequence<Group<T>> {
    return input.groupBy((inputElement) => extractKey(inputElement))
            .pairs()
            .map( extractPair )
            .map((pair) => {
                return {
                    key: pair.key,
                    count: pair.elements
                        .filter(e => e.qnAnswer != null)
                        .filter(e => e.qnAnswer.questionId.toString() == pair.key)
                        //.map(e => ctx.getSessionData().getQuestionById(e.qnAnswer.questionId))
                        .reduce((curr, next) => {
                            const qn = ctx.getSessionData().getQuestionById(next.qnAnswer.questionId);
                            const qnType = SessionData.questionType(qn, false); // choices / list / a freeType
                            // a freeType can be "textarea" / "text" / "date" / "number" / "checkbox" / "money" / "integer" / "list" / "markdown"
                            switch (qnType) {
                                case "number":
                                    return `${parseInt(curr) + parseInt(next.qnAnswer.answer)}`;
                                case "checkbox" :
                                    const ans = SessionData.getAnswerDisplayValue(qn.id, next.qnAnswer.answer);
                                    return `${curr + (ans != null ? 1 : 0)}`;
                                default:
                                    // see SessionData.getAnswerDisplayValue for choices / list / checkbox
                                    return "0";
                            }
                        }, ""),
                    //count: pair.elements.size(),
                    elements: pair.elements
                };
            });
}*/

/*
class GroupReferralAggregateSummaryAccumulator implements Accumulator<ReferralAggregateSummaryTotal, ReferralAggregate> {

    private readonly memo: ReferralAggregateSummaryTotal;

    constructor(private ctx: AnalysisContext) {
        this.memo = {
            totalLength: 0,
            totalItems: 0
        }
    }

    private accumulate(prev: ReferralAggregateSummaryTotal, next: ReferralAggregate) {
        const days = lengthOfDaysOnWaiting(next);
        const curr = {
            totalLength: prev.totalLength + (days || 0),
            totalItems: prev.totalItems + 1
        };
        return curr;
    }

    private postProcess(): void {
    }

    reduce(data: Sequence<ReferralAggregate>): ReferralAggregateSummaryTotal {
        const result = data.reduce( (prev, curr) => this.accumulate(prev, curr), this.memo );
        this.postProcess();
        return result;
    }
}
*/
export class GroupQnAnswersTotalsAnalysis extends SequenceAnalysis<GroupQnAnswerTotals> {
    constructor(ctx: AnalysisContext, data: Sequence<GroupQnAnswerTotals>) {
        super(ctx, data, (item: GroupQnAnswerTotals) => item.key);
        this.derivativeAnalysers = {
            //
        };
        this.recordRepresentation = {
            //
        };
        this.addOnClickAnalyser("ungroup", WrapUnGroupQnAnswersWithRefCountAnalyser);
        this.addOnClickManyAnalysis("ungroup", QnAnswerWithRefAnalysis);
    }
}

// basic count of the number of answers per question
export var questionAnswersCountByQuestionAnalyser: Transformer<QnAnswerWorkWithRefToReferralAggregate, Group<QnAnswerWorkWithRefToReferralAggregate>>
        = function(ctx: AnalysisContext, input: Sequence<QnAnswerWorkWithRefToReferralAggregate>): SequenceAnalysis<Group<QnAnswerWorkWithRefToReferralAggregate>> {
    return new QnAnswersWithRefCountAnalysis(ctx, countsBy(input, a => {
        const qnId = a.qnAnswer?.questionId;
        return qnId
            ? StringUtils.trimText(ctx.getSessionData().getQuestionById(qnId).name, 10) || "unknown"
            : "no answer";
    }));
};
export class QnAnswersWithRefCountAnalysis extends SequenceAnalysis<Group<QnAnswerWorkWithRefToReferralAggregate>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<QnAnswerWorkWithRefToReferralAggregate>>) {
        super(ctx, data, (item: Group<QnAnswerWorkWithRefToReferralAggregate>) => item.key);
        this.derivativeAnalysers = {
            //
        };
        this.recordRepresentation = {
            //
        }
        this.addOnClickAnalyser("ungroup", WrapUnGroupQnAnswersWithRefCountAnalyser);
        this.addOnClickManyAnalysis("ungroup", QnAnswerWithRefAnalysis);
    }
}
const WrapUnGroupQnAnswersWithRefCountAnalyser: Analyser<Group<QnAnswerWorkWithRefToReferralAggregate>, Sequence<QnAnswerWorkWithRefToReferralAggregate>> =
        function (ctx: AnalysisContext, input: Group<QnAnswerWorkWithRefToReferralAggregate>): QnAnswerWithRefAnalysis {
            return new QnAnswerWithRefAnalysis(ctx, input.elements);
        };

export var qnAnswerWorkFromReferralAggregateAnalyser: Transformer<
    ReferralAggregate,
    QnAnswerWorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<QnAnswerWorkWithRefToReferralAggregate> {
    return new QnAnswerWithRefAnalysis(ctx, flattenQuestionnaireWork(input));
};
export function flattenQuestionnaireWork(
    input: Sequence<ReferralAggregate>
): Sequence<QnAnswerWorkWithRefToReferralAggregate> {
    let allWork: Sequence<QnAnswerWorkWithRefToReferralAggregate> = input
        .filter(ra => ra != null)
        .map(ra => {
            if (ra.questionnaireWork?.size() == 0) {
                const workWithRA: QnAnswerWorkWithRefToReferralAggregate = {
                    parent: ra,
                    work: null,
                    qnAnswer: null
                };
                return workWithRA;
            } else {
                return ra.questionnaireWork!.map(work => {
                    if (work.answers.length == 0) {
                        const placeholderAnswer: QuestionAnswerSnapshotDto = {
                            id: 0,
                            questionId: 0,
                            answer: "no-answer"
                        };
                        return ensureQnAnswerWorkReferencesItem(ra, work, placeholderAnswer);
                    } else {
                        return work.answers.map(ans =>
                            ensureQnAnswerWorkReferencesItem(ra, work, ans)
                        );
                    }
                });
            }
        })
        .flatten<QnAnswerWorkWithRefToReferralAggregate>();
    return allWork;
}
export function ensureQnAnswerWorkReferencesItem(
    item: ReferralAggregate,
    work: evidenceDto.BaseWork,
    ans: QuestionAnswerSnapshotDto
): QnAnswerWorkWithRefToReferralAggregate {
    let result: QnAnswerWorkWithRefToReferralAggregate = {
        work: work,
        qnAnswer: ans,
        parent: item
    };
    return result;
}

//*********************************
// Analysis: QuestionnaireMultiSnapshotOnlyAnalysis
// useful really only as debugging but kicks off the flattening, by question and matrix analyser
// data is as-given - a SnapshotPeriod ordered collection of the pre/during1/during2
// but each dto may be split across another dto due to paging, so each dto is not unique by serviceRecipientId
export class QuestionnaireSnapshotOnlyAnalysis extends SequenceAnalysis<QuestionnaireAnswersSnapshotDto> {
    constructor(ctx: AnalysisContext, data: Sequence<QuestionnaireAnswersSnapshotDto>) {
        super(ctx, data, (item: QuestionnaireAnswersSnapshotDto) => ""); // keyFn null since this analyser not intended to be used in Group
        this.derivativeAnalysers = {
            // do more useful things with the data by flattening with the answers
            questionnaireAnswersSnapshotFlatten: questionnaireAnswersSnapshotFlattenAnalyser,
            flattenToReferralSummary: flattenToReferralSummaryAnalyser
        };
        this.recordRepresentation = {
            // if debugging, a table representation may be useful
        };
    }
}

// TODO generalise this and put in predefined-table-representation
const flattenToReferralSummaryAnalyser: Transformer<
    QuestionnaireAnswersSnapshotDto,
    EntityWithParent<QuestionnaireAnswersSnapshotDto, ReferralSummaryDto>
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionnaireAnswersSnapshotDto>
): SequenceAnalysis<EntityWithParent<QuestionnaireAnswersSnapshotDto, ReferralSummaryDto>> {
    // opens in referralAnalysis, but comes back with
    return new ReferralSummaryAnalysis(
        ctx,
        input.map(qn => {
            return ensureEntityReferencesParent(qn.referralSummary, qn); // qn is the parent, because we're dealing with referralSummary
        })
    );
};
// avoid typing too much with const
const WrapUnGroupToQuestionnaireAnalyser = function (
    ctx: AnalysisContext,
    input: Group<EntityWithParent<QuestionnaireAnswersSnapshotDto, ReferralSummaryDto>>
): QuestionnaireSnapshotOnlyAnalysis {
    return new QuestionnaireSnapshotOnlyAnalysis(
        ctx,
        input.elements.map(e => {
            // un-extract the parent, which is now the bit we want again
            return e.parent;
        })
    );
};
types.analysersByName["WrapUnGroupToQuestionnaireAnalyser"] = WrapUnGroupToQuestionnaireAnalyser; // this extracts the group.elements

//*********************************
// Analysis: questionnaireAnswersSnapshotFlattenAnalyser
// useful in getting the actual answers with reference to the parent

let questionAnswerSnapshotWithParentColumns = columnMap(
    // TODO join onto ReferralSummary for the columns there
    textColumn<QuestionAnswerSnapshotDtoWithParent>(
        "r-id",
        row =>
            row.parent.referralSummary.referralCode ||
            row.parent.referralSummary.referralId!.toString()
    ),
    textColumn<QuestionAnswerSnapshotDtoWithParent>("snapshotPeriod", row =>
        row.parent.snapshotPeriod.toString()
    ),
    dateTimeColumn<QuestionAnswerSnapshotDtoWithParent>("workDate", row =>
        EccoDateTime.parseIso8601(row.workDate!)
    ),
    numberColumn<QuestionAnswerSnapshotDtoWithParent>("questionId", row => row.questionId), //tableRepresentations.questionDefIdLookup(row.questionId, row.reportItem.sessionData)),
    // NB questiongroup is available via "qa: questiongroup"
    textColumn<QuestionAnswerSnapshotDtoWithParent>("questionName", (row, ctx) =>
        tableRepresentations.questionDefIdLookup(row.questionId, ctx.getSessionData())
    ),
    textColumn<QuestionAnswerSnapshotDtoWithParent>("answer", (row, ctx) =>
        tableRepresentations.questionAnswerDisplayValueLookup(
            row.questionId,
            row.answer,
            ctx.getSessionData()
        )
    )
);

let questionAnswerSnapshotToSummaryColumns = joinNestedPathColumnMaps<
    QuestionAnswerSnapshotDtoWithParent,
    ReferralSummaryDto
>("r", row => row.parent.referralSummary, referralSummaryColumns);
let questionAnswerSnapshotToClientColumns = joinNestedPathColumnMaps<
    QuestionAnswerSnapshotDtoWithParent,
    Client
>("c", row => row.parent.client, clientOnlyColumns);
// convert a QuestionAnswerSnapshot into a QnAnswerWork which has a QuestionAnswerSnapshot - so we can re-use the fields, albeit not many
let questionAnswerSnapshotToQAColumns = joinNestedPathColumnMaps<
    QuestionAnswerSnapshotDtoWithParent,
    QnAnswerWork
>(
    "qa",
    row => {
        return {work: null, qnAnswer: {...row}};
    },
    qnAnswerWorkOnlyColumns
);
let questionAnswerSnapshotColumns = joinColumnMaps(
    questionAnswerSnapshotWithParentColumns,
    questionAnswerSnapshotToQAColumns,
    questionAnswerSnapshotToSummaryColumns,
    questionAnswerSnapshotToClientColumns
);

let questionnaireAnswersSnapshotFlattenAnalyser: Transformer<
    QuestionnaireAnswersSnapshotDto,
    QuestionAnswerSnapshotDtoWithParent
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionnaireAnswersSnapshotDto>
): SequenceAnalysis<QuestionAnswerSnapshotDtoWithParent> {
    return new QuestionAnswerSnapshotWithParentAnalysis(
        ctx,
        flattenQuestionnaireAnswersSnapshot(input)
    );
};
class QuestionAnswerSnapshotWithParentAnalysis extends SequenceAnalysis<QuestionAnswerSnapshotDtoWithParent> {
    constructor(ctx: AnalysisContext, data: Sequence<QuestionAnswerSnapshotDtoWithParent>) {
        super(ctx, data, (item: QuestionAnswerSnapshotDtoWithParent) => ""); // keyFn null since this analyser not intended to be used in Group
        this.derivativeAnalysers = {
            questionAnswerSnapshotFilterChoices: questionAnswerMultiSnapshotFilterChoicesAnalyser,
            questionAnswerSnapshotFilterInteger: questionAnswerMultiSnapshotFilterIntegerAnalyser,
            questionAnswerByQuestionSummary: questionAnswerByQuestionSummaryAnalyser,

            questionAnswerSnapshotTransposeByCid: questionAnswerSnapshotTransposeByCidAnalyser,
            questionAnswerSnapshotTransposeBySrId: questionAnswerSnapshotTransposeBySrIdAnalyser,

            // filter by choices, since matrix tables only make sense with defined values
            questionAnswerMultiSnapshotFilterChoices:
                questionAnswerMultiSnapshotFilterChoicesAnalyser,
            // sort by question into a matrix per question
            questionAnswerMultiSnapshotMatrixByQuestion:
                questionnaireMultiSnapshotMatrixByQuestionAnalyser,
            // filter by integers, since difference tables only make sense with integer values
            questionAnswerMultiSnapshotFilterInteger:
                questionAnswerMultiSnapshotFilterIntegerAnalyser,
            // sort by question into a difference per question
            questionAnswerMultiSnapshotDifferenceByQuestion:
                questionnaireMultiSnapshotDifferenceByQuestionAnalyser
        };
        this.recordRepresentation = {
            questionAnswerSnapshotWithParent: questionAnswerSnapshotColumns
        };
    }
}

//*******************************
//*******************************
//           MATRIX
//*******************************
//*******************************

//*********************************
// Analysis: QuestionAnswerSnapshotWithParentAnalysis
// take out the questions that don't have multiple answers

let questionAnswerMultiSnapshotFilterChoicesAnalyser: Transformer<
    QuestionAnswerSnapshotDtoWithParent,
    QuestionAnswerSnapshotDtoWithParent
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): SequenceAnalysis<QuestionAnswerSnapshotDtoWithParent> {
    return new QuestionAnswerSnapshotWithParentAnalysis(
        ctx,
        filterChoicesQuestionnaireAnswersSnapshot(input, ctx)
    );
};
function filterChoicesQuestionnaireAnswersSnapshot(
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>,
    ctx: AnalysisContext
): Sequence<QuestionAnswerSnapshotDtoWithParent> {
    let filterChoicesOnly: Sequence<QuestionAnswerSnapshotDtoWithParent> = input.filter(
        snapshot => ctx.getSessionData().getQuestionById(snapshot.questionId).choices.length > 0
    );
    // technically on the schema, this might include freeTypes
    return filterChoicesOnly;
}

//*********************************
// Analysis:

let questionAnswerByQuestionSummaryAnalyser: Transformer<
    QuestionAnswerSnapshotDtoWithParent,
    CountsByMonthDto
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): SequenceAnalysis<CountsByMonthDto> {
    const byQnGroups: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> = groupBy(
        input,
        groupByQuestionId
    );
    const data = byQnGroups
        .map(byQnGroup => {
            let sessionData = ctx.getSessionData();
            const qn = sessionData.getQuestionById(byQnGroup.elements.first().questionId);
            const byQnAnsGroups: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> = groupBy(
                byQnGroup.elements,
                groupByAnswer
            );
            return byQnAnsGroups.map(byQnAns => {
                const answer = byQnAns.elements.first().answer;
                const answerDisplay = sessionData.getAnswerDisplayValue(qn.id, answer);
                const counts: Partial<CountsByMonthDto> = {
                    entityName: qn.name,
                    entityName2: answerDisplay,
                    count: byQnAns.elements.size()
                };
                return counts;
            });
        })
        .flatten<CountsByMonthDto>();

    return new ByMonthAnalysis(ctx, data);
};
/*class QuestionAnswerByQuestionGroupAnalysis extends SequenceAnalysis<Group<SomeRow>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<SomeRow>>) {
        super(ctx, data, (item: SomeRow) => ""); // keyFn null since this analyser not intended to be used in Group
        this.derivativeAnalysers = {
            //"questionAnswerMultiSnapshotDifferenceByQuestion": questionnaireMultiSnapshotDifferenceByQuestionAnalyser
        };
        this.recordRepresentation = {
            //"questionAnswerSnapshotWithParent": questionAnswerSnapshotColumns
        }
    }
}*/

//*********************************
// Analysis: QuestionnaireMultiSnapshotMatrixAnalysis

let questionnaireMultiSnapshotMatrixByQuestionAnalyser: Transformer<
    QuestionAnswerSnapshotDtoWithParent,
    Group<QuestionnaireSnapshotMatrixRow>
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): SequenceAnalysis<Group<QuestionnaireSnapshotMatrixRow>> {
    let sessionData = ctx.getSessionData();
    let inputByQuestionIdGroup: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> = groupBy(
        input,
        groupByQuestionId
    );
    let matrixByQn = inputByQuestionIdGroup.map(grp => {
        // the rows for the questionId in the group
        let rows: Sequence<MatrixRow<QuestionnaireSnapshotChangeEntry>> =
            questionnaireMultiSnapshotBySrIdMatrixAccumulator(ctx.getSessionData(), grp.elements);
        // count the elements in the columns
        let countSeq: Sequence<number> = rows
            // one row, or columnGroups is Array<Group<QuestionnaireSnapshotChangeEntry>>
            .map(columnGroups => {
                let totalForRow = 0;
                columnGroups.forEach(columnGroup => {
                    totalForRow += columnGroup.elements.size();
                });
                return totalForRow;
            });
        let count = countSeq.reduce((n: number, cellSize: number) => n + cellSize, 0);
        let displayKey = sessionData.getQuestionById(Number(grp.key)).name;
        return groupObject<QuestionnaireSnapshotMatrixRow>(displayKey, rows, count);
    });
    return new QuestionnaireMultiSnapshotMatrixGroupAnalysis(ctx, matrixByQn);
};
function groupByQuestionId(
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> {
    return input
        .groupBy(inputElement => inputElement.questionId.toString() || "no question assigned")
        .pairs()
        .map(extractPair);
}
function groupByAnswer(
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> {
    return input
        .groupBy(inputElement => inputElement.answer || "no answer assigned")
        .pairs()
        .map(extractPair);
}
function groupBy<T>(input: Sequence<T>, groupFn: GroupFn<T>): Sequence<Group<T>> {
    return groupFn(input).map(pair => {
        let input: Sequence<T> = pair.elements;
        return groupObject<T>(pair.key, input, input.size());
    });
}
function groupObject<T>(key: string, input: Sequence<T>, count: number): Group<T> {
    return {
        key: key,
        count: count,
        elements: input
    };
}
// ANALYSIS
export class QuestionnaireMultiSnapshotMatrixGroupAnalysis extends SequenceAnalysis<
    Group<QuestionnaireSnapshotMatrixRow>
> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<QuestionnaireSnapshotMatrixRow>>) {
        super(ctx, data, (item: Group<QuestionnaireSnapshotMatrixRow>) => item.key);
        // show the matrix for the sequence
        // NB this expects ONLY ONE question at a time, so ensure we have the byQuestion analsyer/chart first
        this.addOnClickAnalyser("ungroup", ungroupToQuestionnaireMultiSnapshotMatrixAnalyser);
        // ?? don't want click-many here
        // this.addOnClickManyAnalysis("ungroup", QuestionnaireMultiSnapshotMatrixAnalysis);
    }
}
type QuestionnaireMultiSnapshotMatrixAnalyser = Analyser<
    Group<QuestionnaireSnapshotMatrixRow>,
    Sequence<QuestionnaireSnapshotMatrixRow>
>;
let ungroupToQuestionnaireMultiSnapshotMatrixAnalyser: QuestionnaireMultiSnapshotMatrixAnalyser =
    function (
        ctx: AnalysisContext,
        input: Group<QuestionnaireSnapshotMatrixRow>
    ): SequenceAnalysis<QuestionnaireSnapshotMatrixRow> {
        return new QuestionnaireMultiSnapshotMatrixAnalysis(ctx, input.elements);
    };

//*********************************
// Analysis: questionAnswerSnapshotWithParentByQuestionAnalyser
// NB ARCHIVED as this section sorts snapshots by questionId, not the end result questionId - which is often wanted with snapshots
/*
type QuestionAnswerSnapshotWithParentGroupAnalyser = Analyser<Sequence<QuestionAnswerSnapshotDtoWithParent>, Sequence<Group<QuestionAnswerSnapshotDtoWithParent>>>;

// ANALYSER
let questionAnswerSnapshotWithParentByQuestionAnalyser: QuestionAnswerSnapshotWithParentGroupAnalyser
    = function(input: Sequence<QuestionAnswerSnapshotDtoWithParent>): SequenceAnalysis<Group<QuestionAnswerSnapshotDtoWithParent>> {
        let answers: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> = groupBy<QuestionAnswerSnapshotDtoWithParent>(input, groupByQuestionId);
        return new GroupedQuestionAnswerSnapshotWithParentAnalysis(answers);
    };

class GroupedQuestionAnswerSnapshotWithParentAnalysis extends SequenceAnalysis<Group<QuestionAnswerSnapshotDtoWithParent>> {
    constructor(data: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>>) {
        super(data, (item: Group<QuestionAnswerSnapshotDtoWithParent>) => item.key);
        this.addOnClickAnalyser("ungroup", ungroupToQuestionAnswerSnapshotWithParentAnalyser);
    }
}
/!** This deals with clicking on chart segment *!/
var ungroupToQuestionAnswerSnapshotWithParentAnalyser: Analyser<Group<QuestionAnswerSnapshotDtoWithParent>,Sequence<QuestionAnswerSnapshotDtoWithParent>> =
    function(input: Group<QuestionAnswerSnapshotDtoWithParent>): QuestionAnswerSnapshotWithParentAnalysis {
        return new QuestionAnswerSnapshotWithParentAnalysis(input.elements);
    };

function groupBy<T>(input: Sequence<T>, groupFn: GroupFn<T>): Sequence<Group<T>> {
    return groupFn(input)
        .map((pair) => {
            let input: Sequence<T> = pair.elements;
            return groupObject<T>(pair.key, input);
        });
}
function groupObject<T>(key: string, input: Sequence<T>): Group<T> {
    return {
        key: key,
        count: input.size(),
        elements: input
    };
}
function groupByQuestionId(input: Sequence<QuestionAnswerSnapshotDtoWithParent>): Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> {
    return input.groupBy((inputElement) =>
        inputElement.questionId.toString() || "no question assigned")
        .pairs()
        .map( extractPair );
}
*/

//*********************************
// Analysis: QuestionnaireMultiSnapshotMatrixAnalysis
// NB The matrix is ONLY designed for showing ONE question's movement at a time

// ANALYSER
// let questionnaireMultiSnapshotMatrixAnalyser: Transformer<QuestionAnswerSnapshotDtoWithParent, QuestionnaireSnapshotMatrixRow>
//     = function(input: Sequence<QuestionAnswerSnapshotDtoWithParent>): SequenceAnalysis<QuestionnaireSnapshotMatrixRow> {
//     return new QuestionnaireMultiSnapshotMatrixAnalysis(questionnaireMultiSnapshotBySrIdMatrixAccumulator(input));
// };

// ANALYSIS
export class QuestionnaireMultiSnapshotMatrixAnalysis extends MatrixAnalysis<QuestionnaireSnapshotMatrixRow> {
    constructor(ctx: AnalysisContext, data: Sequence<QuestionnaireSnapshotMatrixRow>) {
        super(ctx, data);
        this.derivativeAnalysers = {
            //
        };
        // ReportStagesControl.onTableDataClick looks at the stage definition's 'selectionAnalyser', and finds it here
        // and applies it to the current clicked data. The contract of the Analyser is such that it must accept the same
        // type. Therefore an analysis of MatrixRow doesn't work with breakdown data unless the analysis is of
        // a type that is the lowest common denominator - a cell, from which we can Group up (so MatrixRow groups a cell)
        this.addOnClickAnalyser("singleCell", toQuestionAnswerSnapshotWithParentAnalysis);
    }
}

let toQuestionAnswerSnapshotWithParentAnalysis: Analyser<
    QuestionnaireSnapshotMatrixRow,
    Sequence<QuestionAnswerSnapshotDtoWithParent>
> = function (
    ctx: AnalysisContext,
    columns: MatrixRow<QuestionnaireSnapshotChangeEntry>
): QuestionAnswerSnapshotWithParentAnalysis {
    // input is a MatrixRow which is Array<Group<T>>, which is Array<{key, elements}>
    let flattenedData = Lazy(columns)
        .map(cell =>
            cell.elements.map(cellEntry =>
                // map each of the cell contents (a pre/post) to an array and flatten them all together
                [cellEntry.answerPre].concat(cellEntry.answerPost)
            )
        )
        .flatten<QuestionAnswerSnapshotDtoWithParent>();
    return new QuestionAnswerSnapshotWithParentAnalysis(ctx, flattenedData);
};

// ACCUMULATOR
export function questionnaireMultiSnapshotBySrIdMatrixAccumulator(
    sessionData: SessionData,
    flattenedAnswersIn: Sequence<QuestionAnswerSnapshotDtoWithParent>
): Sequence<QuestionnaireSnapshotMatrixRow> {
    // enforce our expectation that we only show for one question
    let allQuestionIds = flattenedAnswersIn
        .map(flatAnswer => flatAnswer.questionId)
        .uniq()
        .toArray();
    if (allQuestionIds.length > 1) {
        throw new Error("matrix table only expects to show answers over one question");
    }

    // determine if the question is a choice question, because if it is,
    // then the config may not have the numerical answer 'value' set correctly
    // which means it will instead be the id of one of the choice
    // so, if we find the answer 'value' matches an id, then we should use its displayValue
    // NB this WILL be a PROBLEM if a choice's 'value' matches a question's choice id
    let qn = allQuestionIds.length == 1 ? sessionData.getQuestionById(allQuestionIds[0]) : null;
    const qnIsChoice = qn ? SessionData.questionType(qn, false) == "choices" : false;

    const flattenedAnswerValues = flattenedAnswersIn.map(flatAnswer => {
        const answerValueIsId = qn?.choices?.some(c => c.id.toString() == flatAnswer.answer);
        // if we match a choice id then use the displayValue as the answer
        if (qnIsChoice && answerValueIsId) {
            return {
                ...flatAnswer,
                answer: sessionData.getAnswerDisplayValue(flatAnswer.questionId, flatAnswer.answer)
            };
        } else {
            return flatAnswer;
        }
    });

    const allAnswerValuesDisplay = flattenedAnswerValues.map(a => a.answer).uniq();

    // get all the answers upfront because we want to render all the columns even if data isn't there
    // NB this specifies the column order
    const alphaSort = (a: string, b: string) => (a < b ? -1 : a > b ? 1 : 0);
    let allAnswerValuesSorted = allAnswerValuesDisplay
        // in a matrix (which this method is) the sort order is often numeric, so we change the order here
        // to sort by any numbers we find in the answers
        .sort((a, b) => {
            const numA = parseInt(a);
            const numB = parseInt(b);
            return isNaN(numA) || isNaN(numB) ? alphaSort(a, b) : numA - numB;
        })
        .toArray();

    // group by srId so we can work out the pre-post values
    let groupBySrId: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> = groupSnapshots(
        flattenedAnswerValues,
        groupBySnapshotSrId
    );

    // for each srId get a pre and post value (with the data points as QuestionAnswerSnapshotDtoWithParent)
    // as intermediate data for the accumulator
    const srIdToCellData = srIdChangeData(
        groupBySrId
    ) as Sequence<QuestionnaireSnapshotChangeEntry>;

    // accumulate the data into rows of pre answer, where each row has columns of post answers => QuestionnaireSnapshotMatrixCell[]
    return Lazy(allAnswerValuesSorted).map(answerValue => {
        return new QuestionnaireSnapshotMatrixAccumulator(
            answerValue,
            allAnswerValuesSorted
        ).reduce(srIdToCellData);
    });
}

interface QuestionAnswerSnapshotDtoWithParent extends QuestionAnswerSnapshotDto {
    parent: QuestionnaireAnswersSnapshotDto;
}
export function flattenQuestionnaireAnswersSnapshot(
    input: Sequence<QuestionnaireAnswersSnapshotDto>
): Sequence<QuestionAnswerSnapshotDtoWithParent> {
    let flattened: Sequence<QuestionAnswerSnapshotDtoWithParent> = input
        .filter(
            snapshot => snapshot != null && snapshot.answers != null && snapshot.answers.length > 0
        )
        .map(snapshot =>
            snapshot.answers.map(answer =>
                ensureQuestionAnswerSnapshotDtoReferencesParent(snapshot, answer)
            )
        )
        .flatten<QuestionAnswerSnapshotDtoWithParent>();
    return flattened;
}
function ensureQuestionAnswerSnapshotDtoReferencesParent(
    parent: QuestionnaireAnswersSnapshotDto,
    answer: QuestionAnswerSnapshotDto
): QuestionAnswerSnapshotDtoWithParent {
    let result = <QuestionAnswerSnapshotDtoWithParent>answer;
    result.parent = parent;
    return result;
}
//interface QuestionAnswerSnapshotDtoWithParentGroup extends Group<QuestionAnswerSnapshotDtoWithParent> {}
interface GroupQuestionAnswerSnapshotDtoWithParent {
    (input: Sequence<QuestionAnswerSnapshotDtoWithParent>): Sequence<
        Group<QuestionAnswerSnapshotDtoWithParent>
    >;
}
function groupSnapshots(
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>,
    groupFn: GroupQuestionAnswerSnapshotDtoWithParent
): Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> {
    return groupFn(input).map(pair => {
        return {
            key: pair.key,
            elements: pair.elements // QuestionAnswerSnapshotDtoWithParent
        };
    });
}
function groupBySnapshotSrId(
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> {
    return input
        .groupBy(inputElement => inputElement.parent.serviceRecipientId.toString())
        .pairs()
        .map(extractPair);
}
function srIdChangeData(
    srIdsWithAnswers: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>>
): Sequence<QuestionnaireSnapshotChangeEntry | null> {
    return (
        srIdsWithAnswers
            .map(srIdWithAnswers => {
                // ASSUME order is pre -> during1 -> during2 (which it is - see  the load order in reportDataSourceFactory)
                let srIdAllAnswersOrderByPeriod: QuestionAnswerSnapshotDtoWithParent[] =
                    srIdWithAnswers.elements.toArray();

                // ordered by period means we get the 'from' answers first
                // so here we have the same srId, with the answers in date order
                // which means we can calculate the pre and post values for the
                // answers given (of which there can only be a max of 3 answers)
                if (srIdAllAnswersOrderByPeriod.length >= 2) {
                    // 3 snapshot answers means we take the first and last
                    // NB the second and third data point can be the exact same data point
                    // as they satisfy both the earliest and latest within the range
                    if (srIdAllAnswersOrderByPeriod.length == 3) {
                        let cell: QuestionnaireSnapshotChangeEntry = {
                            answerPre: srIdAllAnswersOrderByPeriod[0], // this will be PRE
                            answerPost: srIdAllAnswersOrderByPeriod[2] // this will be DURING2
                        };
                        return cell;
                        // otherwise we take the order given
                    } else if (srIdAllAnswersOrderByPeriod.length == 2) {
                        let cell: QuestionnaireSnapshotChangeEntry = {
                            answerPre: srIdAllAnswersOrderByPeriod[0], // this could be PRE or DURING1
                            answerPost: srIdAllAnswersOrderByPeriod[1] // this could be DURING1 or DURING2
                        };
                        // check they are not the exact same data item - which can happen
                        // if they satisfy both the earliest and latest within the range
                        if (cell.answerPre.id == cell.answerPost.id) {
                            return null;
                        }
                        return cell;
                    }
                }
                // there is a chance we don't have 2 data points, so there is no pre-post
                return null;
            })
            // filter out those that don't have data points
            .filter(srIdCell => srIdCell != null)
    );
}
// each cell entry represents movement of a pre to post value
// we need to store the srId's for each cell to indicate who has moved
// but instead we store the pre-post answers so that we can show a breakdown
interface QuestionnaireSnapshotChangeEntry {
    answerPre: QuestionAnswerSnapshotDtoWithParent;
    answerPost: QuestionAnswerSnapshotDtoWithParent;
}

// MATRIX
// for one pre answer, store the columns of post answers
export interface QuestionnaireSnapshotMatrixRow
    extends MatrixRow<QuestionnaireSnapshotChangeEntry> {}
interface QuestionnaireSnapshotMatrixRowMemo extends QuestionnaireSnapshotMatrixRow {}
// accumulator to take one pre answerValue into cells by post answerValue
class QuestionnaireSnapshotMatrixAccumulator
    implements Accumulator<QuestionnaireSnapshotMatrixRow, QuestionnaireSnapshotChangeEntry>
{
    private memo: QuestionnaireSnapshotMatrixRowMemo;
    // key is the pre 'answerValue' this row represents
    constructor(private preAnswerRowKey: string, private allAnswerValues: string[]) {
        this.memo = this.initColumns();
    }
    // prime the post columns with the answerValue as the heading/lookup
    private initColumns(): Array<Group<QuestionnaireSnapshotChangeEntry>> {
        let columns: Array<Group<QuestionnaireSnapshotChangeEntry>> = [];
        this.allAnswerValues.forEach(answerValue => {
            let column = {
                key: answerValue,
                elements: Lazy([])
            };
            columns.push(column);
        });
        return columns;
    }
    // accumulate the cell data into the right column
    private accumulate(
        currentMemo: QuestionnaireSnapshotMatrixRowMemo,
        nextSrIdCell: QuestionnaireSnapshotChangeEntry
    ): QuestionnaireSnapshotMatrixRowMemo {
        // given we are processing one row of pre answers, see if the pre value matches
        let preAnswerMatches = this.preAnswerRowKey == nextSrIdCell.answerPre.answer;
        // if matches, we lookup the correct column via the post answer, to get a cell and push the data in
        if (preAnswerMatches) {
            currentMemo.filter(group => group.key == nextSrIdCell.answerPost.answer)[0].elements =
                currentMemo
                    .filter(group => group.key == nextSrIdCell.answerPost.answer)[0]
                    .elements.concat(nextSrIdCell);
            //let cellData: Sequence<QuestionnaireSnapshotChangeEntry> = currentMemo.filter(group => group.key == nextSrIdCell.answerPost.answer)[0].elements;
            // push the cell data into that cell
            //cellData.push(nextSrIdCell);
        }
        return currentMemo;
    }
    reduce(srIdCell: Sequence<QuestionnaireSnapshotChangeEntry>): QuestionnaireSnapshotMatrixRow {
        let result = srIdCell.reduce(
            (currentMemo, nextSrIdCell) => this.accumulate(currentMemo, nextSrIdCell),
            this.memo
        );
        return result;
    }
}

//*******************************
//*******************************
//          DIFFERENCE
//*******************************
//*******************************

//*********************************
// Analysis: QuestionAnswerSnapshotWithParentAnalysis
// take out the questions that don't have integer answers

let questionAnswerMultiSnapshotFilterIntegerAnalyser: Transformer<
    QuestionAnswerSnapshotDtoWithParent,
    QuestionAnswerSnapshotDtoWithParent
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): SequenceAnalysis<QuestionAnswerSnapshotDtoWithParent> {
    return new QuestionAnswerSnapshotWithParentAnalysis(
        ctx,
        filterIntegerQuestionnaireAnswersSnapshot(input, ctx)
    );
};
function filterIntegerQuestionnaireAnswersSnapshot(
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>,
    ctx: AnalysisContext
): Sequence<QuestionAnswerSnapshotDtoWithParent> {
    // possibly only makes sense for money, as it's calculating the difference in the snapshot data
    let filterIntegerOnly: Sequence<QuestionAnswerSnapshotDtoWithParent> = input.filter(snapshot =>
        ctx
            .getSessionData()
            .getQuestionById(snapshot.questionId)
            .freeTypes.some(ft => ft.valueType == "money")
    );
    // technically on the schema, this might include choices and other freetypes, but practically it won't
    return filterIntegerOnly;
}

//*********************************
// Analysis: QuestionnaireMultiSnapshotDifferenceAnalysis

// clicking a row of: questionId, direction, difference, gives the breakdown of the group<answers>
// donut chart for direction - +ve, -ve, unchanged; values of number of clients (inner) and value (outer)
// donut chart per questionId showing number of clients (inner) and value (outer)
// breakdown table of group<answersWithParent> for the selected key

/**
 * A difference and the direction of the difference for each change
 */
export interface QuestionnaireSnapshotDifference extends QuestionnaireSnapshotChangeEntry {
    difference: number;
    direction: string;
}

/**
 * Wrapper fn for what kind of groupBy value the pie chart should show.
 * Here we choose the value as the number of srIds
 * but we could total the difference reduced across all srIds.
 */
function groupFnBySrIdCount(
    key: string,
    input: Sequence<QuestionnaireSnapshotDifference>
): Group<QuestionnaireSnapshotDifference> {
    return groupObject<QuestionnaireSnapshotDifference>(
        key,
        input,
        input.size() // each element in the sequence represents an srId change, so this is an srId count
    );
}

function groupFnBySrIdDifference(
    key: string,
    input: Sequence<QuestionnaireSnapshotDifference>
): Group<QuestionnaireSnapshotDifference> {
    const count = input.reduce((totalDiff, diff) => totalDiff + diff.difference, 0); // each element in the sequence represents an srId change, so count the total difference
    const qnGainLoss = count > 0 ? key.concat(" (up)") : key.concat(" (down)");
    return groupObject<QuestionnaireSnapshotDifference>(qnGainLoss, input, Math.abs(count));
}

/**
 * A difference report shows numeric changes on an srId over a questionId (those with an 'integer' freetype).
 * We use one analyser to build up the data since the calculations only make sense together.
 */
let questionnaireMultiSnapshotDifferenceByQuestionAnalyser: Transformer<
    QuestionAnswerSnapshotDtoWithParent,
    Group<QuestionnaireSnapshotDifference>
> = function (
    ctx: AnalysisContext,
    input: Sequence<QuestionAnswerSnapshotDtoWithParent>
): SequenceAnalysis<Group<QuestionnaireSnapshotDifference>> {
    // get all the raw answers by questionId
    let inputByQuestionIdGroup: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> = groupBy(
        input,
        groupByQuestionId
    );

    // translate each questionId into a group of srId differences
    let differenceByQnBySrId = inputByQuestionIdGroup.map(grp => {
        // for this question, group by srId
        // each group could have 1, 2 or 3 values since they are snapshot values
        let groupBySrId: Sequence<Group<QuestionAnswerSnapshotDtoWithParent>> = groupSnapshots(
            grp.elements,
            groupBySnapshotSrId
        );

        // translate each srId group of 1, 2 or 3 snapshot values into the correct pre/post values
        // resulting in an srId change per entry in the sequence
        let changesBySrId = srIdChangeData(groupBySrId);

        // calculate the difference for each sequence item
        // which is for each srId and questionId
        let differenceBySrId: Sequence<QuestionnaireSnapshotDifference> = changesBySrId.map(
            changeBySrId => {
                if (!changeBySrId) return {difference: 0} as QuestionnaireSnapshotDifference;
                // up-cast to QuestionnaireSnapshotDifference
                let ssDiff = <QuestionnaireSnapshotDifference>changeBySrId;
                let difference =
                    parseInt(changeBySrId.answerPost.answer) -
                    parseInt(changeBySrId.answerPre.answer);
                let direction = difference > 0 ? "up" : difference < 0 ? "down" : "none";
                ssDiff.difference = difference;
                ssDiff.direction = direction;
                return ssDiff;
            }
        );

        // if we have data, then get the session data
        let sessionData = ctx.getSessionData();

        // return the question group with the newly calculated answers
        let displayKey = sessionData.getQuestionById(Number(grp.key)).name;
        return groupFnBySrIdDifference(displayKey, differenceBySrId);
    });

    return new QuestionnaireMultiSnapshotDifferenceGroupAnalysis(ctx, differenceByQnBySrId);
};

// ANALYSIS
// this allow a pie chart by question with value of count of srIds contributing to the change
export class QuestionnaireMultiSnapshotDifferenceGroupAnalysis extends SequenceAnalysis<
    Group<QuestionnaireSnapshotDifference>
> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<QuestionnaireSnapshotDifference>>) {
        super(ctx, data, (item: Group<QuestionnaireSnapshotDifference>) => item.key); // the questionName
        // show the difference for the sequence
        // NB this expects ONLY ONE question at a time, so ensure we have the byQuestion analsyer/chart first
        this.addOnClickAnalyser("ungroup", ungroupToQuestionnaireMultiSnapshotDifferenceAnalyser);
        // ?? don't want click-many here
        // this.addOnClickManyAnalysis("ungroup", ...
        //this.recordRepresentation = {
        //}
    }
}

let ungroupToQuestionnaireMultiSnapshotDifferenceAnalyser: Analyser<
    Group<QuestionnaireSnapshotDifference>,
    Sequence<QuestionnaireSnapshotDifference>
> = function (
    ctx: AnalysisContext,
    input: Group<QuestionnaireSnapshotDifference>
): SequenceAnalysis<QuestionnaireSnapshotDifference> {
    return new QuestionnaireMultiSnapshotDifferenceAnalysis(ctx, input.elements);
};
// ANALYSIS
export class QuestionnaireMultiSnapshotDifferenceAnalysis extends SequenceAnalysis<QuestionnaireSnapshotDifference> {
    constructor(ctx: AnalysisContext, data: Sequence<QuestionnaireSnapshotDifference>) {
        super(ctx, data, (item: QuestionnaireSnapshotDifference) =>
            item.answerPre.questionId.toString()
        ); // Took a guess at a key
        this.derivativeAnalysers = {
            //"flattenWithSnapshotData": toQuestionAnswerSnapshotWithParentFromDifferenceAnalyser
        };
        this.recordRepresentation = {
            differenceColumns: differenceOnlyColumns
        };
    }
}
const differenceOnlyColumns = columnMap(
    numberColumn<QuestionnaireSnapshotDifference>("difference", row => row.difference),
    textColumn<QuestionnaireSnapshotDifference>("direction", row => row.direction),
    textColumn<QuestionnaireSnapshotDifference>(
        "r-id",
        row =>
            row.answerPre.parent.referralSummary.referralCode ||
            row.answerPre.parent.referralSummary.referralId!.toString()
    ),
    textColumn<QuestionnaireSnapshotDifference>(
        "c-id",
        row =>
            row.answerPre.parent.referralSummary.clientCode ||
            row.answerPre.parent.referralSummary.clientId.toString()
    ),
    numberColumn<QuestionnaireSnapshotDifference>(
        "referralId",
        row => row.answerPre.parent.referralSummary.referralId
    ),
    numberColumn<QuestionnaireSnapshotDifference>(
        "clientId",
        row => row.answerPre.parent.referralSummary.clientId
    ),
    textColumn<QuestionnaireSnapshotDifference>("answerPre", row => row.answerPre.answer),
    textColumn<QuestionnaireSnapshotDifference>("answerPost", row => row.answerPost.answer)
);

// TODO flatten to the raw data and join
// var toQuestionAnswerSnapshotWithParentFromDifferenceAnalyser: Analyser<Group<QuestionnaireSnapshotDifference>, Sequence<QuestionAnswerSnapshotDtoWithParent>> =
//     function(input: Group<QuestionnaireSnapshotChangeEntry>): QuestionAnswerSnapshotWithParentAnalysis {
//         let changeElements = input[0].elements;
//         let changeDataFlattened = changeElements.map(changeEntry => [changeEntry.answerPre].concat(changeEntry.answerPost))
//             .flatten<QuestionAnswerSnapshotDtoWithParent>();
//         return new QuestionAnswerSnapshotWithParentAnalysis(changeDataFlattened);
//     };
//
// var differenceOnlyColumns = columnMap(
//     numberColumn<QuestionnaireSnapshotDifference>("difference", (row) => row.difference),
//     textColumn<QuestionnaireSnapshotDifference>("direction", (row) => row.direction),
// );
// var differenceToQuestionAnswerSnapshotWithParentColumns = joinNestedPathColumnMaps<QuestionAnswerSnapshotDtoWithParent, QuestionAnswerSnapshotDtoWithParent>("answers",
//     (row) => row, questionAnswerSnapshotWithParentColumns);
// var differenceColumns = joinColumnMaps(differenceOnlyColumns, differenceToQuestionAnswerSnapshotWithParentColumns);
